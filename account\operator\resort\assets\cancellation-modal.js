// Cancellation Modal JavaScript

// Handle radio button changes for all cancellation modals
document.addEventListener('change', function(e) {
    if (e.target.name === 'cancellation_reason') {
        const form = e.target.closest('form');
        const bookingId = form.id.replace('cancelForm-', '');
        const submitBtn = document.getElementById(`submitBtn-${bookingId}`);
        const customReasonTextarea = document.getElementById(`customReason-${bookingId}`);
        
        // Enable submit button when any reason is selected
        submitBtn.disabled = false;
        submitBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
        submitBtn.classList.add('bg-red-600', 'hover:bg-red-800');
        
        // Handle "other" option
        if (e.target.value === 'other') {
            customReasonTextarea.disabled = false;
            customReasonTextarea.classList.remove('disabled:bg-gray-100', 'disabled:text-gray-500');
            customReasonTextarea.classList.add('bg-white');
            customReasonTextarea.focus();
        } else {
            customReasonTextarea.disabled = true;
            customReasonTextarea.classList.add('disabled:bg-gray-100', 'disabled:text-gray-500');
            customReasonTextarea.classList.remove('bg-white');
            customReasonTextarea.value = '';
        }
    }
});

// Handle textarea input for custom reason
document.addEventListener('input', function(e) {
    if (e.target.name === 'custom_reason') {
        const form = e.target.closest('form');
        const bookingId = form.id.replace('cancelForm-', '');
        const submitBtn = document.getElementById(`submitBtn-${bookingId}`);
        const otherRadio = form.querySelector('input[name="cancellation_reason"][value="other"]');
        
        if (otherRadio && otherRadio.checked) {
            if (e.target.value.trim() === '') {
                submitBtn.disabled = true;
                submitBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                submitBtn.classList.remove('bg-red-600', 'hover:bg-red-800');
            } else {
                submitBtn.disabled = false;
                submitBtn.classList.remove('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                submitBtn.classList.add('bg-red-600', 'hover:bg-red-800');
            }
        }
    }
});

// Handle form submission
document.addEventListener('submit', function(e) {
    if (e.target.name === 'cancelBooking' || e.target.querySelector('[name="cancelBooking"]')) {
        const form = e.target;
        const selectedReason = form.querySelector('input[name="cancellation_reason"]:checked');
        
        if (!selectedReason) {
            e.preventDefault();
            alert('Please select a reason for cancellation.');
            return false;
        }
        
        if (selectedReason.value === 'other') {
            const customReason = form.querySelector('textarea[name="custom_reason"]').value.trim();
            if (customReason === '') {
                e.preventDefault();
                alert('Please provide a custom reason for cancellation.');
                return false;
            }
        }
        
        // Confirmation prompt
        if (!confirm('Are you sure you want to submit this cancellation request?')) {
            e.preventDefault();
            return false;
        }
    }
});

// Reset modal state when closed
document.addEventListener('click', function(e) {
    if (e.target.hasAttribute('data-modal-hide') && e.target.getAttribute('data-modal-hide').includes('cancel-booking-modal')) {
        const modalId = e.target.getAttribute('data-modal-hide');
        const bookingId = modalId.replace('cancel-booking-modal-', '');
        
        // Reset form
        const form = document.getElementById(`cancelForm-${bookingId}`);
        if (form) {
            form.reset();
            
            // Reset submit button
            const submitBtn = document.getElementById(`submitBtn-${bookingId}`);
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.classList.add('disabled:bg-gray-400', 'disabled:cursor-not-allowed');
                submitBtn.classList.remove('bg-red-600', 'hover:bg-red-800');
            }
            
            // Reset custom reason textarea
            const customReasonTextarea = document.getElementById(`customReason-${bookingId}`);
            if (customReasonTextarea) {
                customReasonTextarea.disabled = true;
                customReasonTextarea.classList.add('disabled:bg-gray-100', 'disabled:text-gray-500');
                customReasonTextarea.classList.remove('bg-white');
                customReasonTextarea.value = '';
            }
        }
    }
});