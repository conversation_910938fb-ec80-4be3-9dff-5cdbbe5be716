<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">


<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-orange-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-orange-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-orange-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-orange-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Canceled</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-ban text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Cancelation Requests</h1>
                    <p class="text-orange-100">Manage booking cancellation requests</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Cancellation Requests Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
            <?php
            try {
                $getBookingStatus = "request";

                $cancellationRequests = getIncompleteBooking($pdo, $getBookingStatus, $id);

                if ($cancellationRequests && count($cancellationRequests) > 0) {
                    foreach ($cancellationRequests as $row) {
                        $isCanceled = isset($row['booking_status']) && $row['booking_status'] === 'request';
                        ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>" data-status="canceled">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $isCanceled ? 'status-pending' : ($bothApproved ? 'status-ready' : '') ?>">
                                        <i class="fas fa-ban mr-1"></i>
                                        <?= $isCanceled ? 'Requesting Cancelation' : ($bothApproved ? 'Canceled' : '') ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-hotel text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Date Requested:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= date('M d, Y', strtotime($row['date_created'] ?? 'now')); ?></span>
                                </div>
                            </div>
                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <a href="view-pending-transaction.php?id=<?= $booking_id; ?>"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-magnifying-glass mr-1.5"></i>
                                    Review Cancelation
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    // No cancellation requests found
                    ?>
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                            <div class="max-w-md mx-auto">
                                <div class="bg-orange-100 p-4 rounded-full inline-block mb-4">
                                    <i class="fas fa-inbox text-orange-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Cancellation Requests</h3>
                                <p class="text-gray-500 mb-4">When cancellation requests are submitted, they will appear here.
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Requests</h3>
                            <p class="text-gray-500 mb-4">There was an error loading the cancelation requests:
                                <?= htmlspecialchars($e->getMessage()); ?>
                            </p>
                            <button onclick="window.location.reload()"
                                class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
    <?php
    require '_footer.php';
    ?>