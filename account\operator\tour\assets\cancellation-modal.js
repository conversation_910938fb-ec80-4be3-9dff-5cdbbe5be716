// Cancellation Modal JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Handle radio button changes for all cancellation modals
    document.addEventListener('change', function(e) {
        if (e.target.name === 'cancellation_reason') {
            const bookingId = e.target.closest('form').id.split('-')[1];
            const customReasonTextarea = document.getElementById(`customReason-${bookingId}`);
            const submitBtn = document.getElementById(`submitBtn-${bookingId}`);
            
            // Enable submit button when any reason is selected
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            submitBtn.classList.add('hover:bg-red-700');
            
            // Handle custom reason textarea
            if (e.target.value === 'other') {
                customReasonTextarea.disabled = false;
                customReasonTextarea.focus();
                customReasonTextarea.required = true;
                
                // Check if custom reason is filled when 'other' is selected
                const checkCustomReason = () => {
                    if (customReasonTextarea.value.trim() === '') {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                        submitBtn.classList.remove('hover:bg-red-700');
                    } else {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                        submitBtn.classList.add('hover:bg-red-700');
                    }
                };
                
                // Add event listener for custom reason input
                customReasonTextarea.addEventListener('input', checkCustomReason);
                
                // Initial check
                checkCustomReason();
            } else {
                customReasonTextarea.disabled = true;
                customReasonTextarea.required = false;
                customReasonTextarea.value = '';
                
                // Remove any existing event listeners
                customReasonTextarea.removeEventListener('input', checkCustomReason);
            }
        }
    });
    
    // Handle form submission validation
    document.addEventListener('submit', function(e) {
        if (e.target.name === 'cancelBooking' || e.target.querySelector('[name="cancelBooking"]')) {
            const form = e.target;
            const selectedReason = form.querySelector('input[name="cancellation_reason"]:checked');
            
            if (!selectedReason) {
                e.preventDefault();
                alert('Please select a reason for cancellation.');
                return false;
            }
            
            if (selectedReason.value === 'other') {
                const customReason = form.querySelector('[name="custom_reason"]');
                if (!customReason.value.trim()) {
                    e.preventDefault();
                    alert('Please provide a custom reason for cancellation.');
                    customReason.focus();
                    return false;
                }
            }
            
            // Confirm submission
            if (!confirm('Are you sure you want to submit this cancellation request?')) {
                e.preventDefault();
                return false;
            }
        }
    });
    
    // Reset modal state when closed
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-modal-hide') && e.target.getAttribute('data-modal-hide').includes('cancel-booking-modal')) {
            const modalId = e.target.getAttribute('data-modal-hide');
            const bookingId = modalId.split('-')[3];
            
            // Reset form
            const form = document.getElementById(`cancelForm-${bookingId}`);
            if (form) {
                form.reset();
                
                // Reset submit button state
                const submitBtn = document.getElementById(`submitBtn-${bookingId}`);
                submitBtn.disabled = true;
                submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                submitBtn.classList.remove('hover:bg-red-700');
                
                // Reset custom reason textarea
                const customReasonTextarea = document.getElementById(`customReason-${bookingId}`);
                customReasonTextarea.disabled = true;
                customReasonTextarea.required = false;
            }
        }
    });
});