<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-blue-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Pending Bookings</h1>
                    <p class="text-blue-100">Bookings awaiting approval from operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php
            try {
                $getMthoStatus = "Pending";
                $getBookingStatus = "pending";

                $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {
                        $booking_id = $row['booking_id'];
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $designationTour = $row['tour_operator_designation'];
                        $destination = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];

                        // Check if the booking is canceled
                        $isCanceled = isset($row['cancellation_booking_status']) && $row['cancellation_booking_status'] === 'request';

                        // Check if both resort and boat are approved
                        if (!$isCanceled && isset($row['mtho']) && $row['mtho'] === "Waiting") {
                            $bothApproved = (isset($row['resort']) && $row['resort'] === "Approved" && isset($row['boat']) && $row['boat'] === "Approved");
                        } else {
                            $bothApproved = false;
                        }

                        if (isset($row['payment_status']) && $row['payment_status'] === 'voucher') {
                            $showBadge = '<span class="badge inline-flex items-center bg-purple-100 text-purple-800 border border-purple-300 rounded-full px-3 py-1 text-xs font-semibold">
                            <i class="fas fa-ticket-alt mr-1 text-purple-500"></i>
                            Voucher
                            </span>';
                        } else {
                            $showBadge = '';
                        }
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card rounded-lg p-6 fade-in"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-designation="<?= htmlspecialchars($designationTour ?? ''); ?>"
                            data-destination="<?= htmlspecialchars($destination ?? ''); ?>"
                            data-status="<?= $isCanceled ? 'canceled' : ($bothApproved ? 'ready' : 'pending'); ?>">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                        <?= $showBadge; ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $isCanceled ? 'status-canceled' : ($bothApproved ? 'status-ready' : 'status-pending'); ?>">
                                        <?php if ($isCanceled): ?>
                                            <i class="fas fa-ban mr-1"></i>
                                            Requesting Cancelation
                                        <?php elseif ($bothApproved): ?>
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Ready for Payment
                                        <?php else: ?>
                                            <i class="fas fa-clock mr-1"></i>
                                            Awaiting Approval
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>


                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-user text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Operator:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($operatorName ?? 'N/A'); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-briefcase text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Designation:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($designationTour ?? 'N/A'); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-map-marker-alt text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Destination:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($destination ?? 'N/A'); ?></span>
                                </div>
                                <?php if (isset($row['check_in_date']) && isset($row['check_out_date'])): ?>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                        <span class="text-gray-600">Date:</span>
                                        <span
                                            class="ml-2 font-medium text-gray-900"><?= htmlspecialchars(date('F d, Y', strtotime($row['check_in_date']))); ?>
                                            -
                                            <?= htmlspecialchars(date('F d, Y', strtotime($row['check_out_date']))); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($row['total_adults']) && isset($row['total_children'])): ?>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                        <span class="text-gray-600">Total Pax:</span>
                                        <span
                                            class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['total_adults'] + $row['total_children']); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <a href="view-pending-transaction.php?id=<?= $booking_id; ?>"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-eye mr-1.5"></i>
                                    View Details
                                </a>
                                <?php if (!$isCanceled): ?>
                                <button data-modal-target="admin-decline-modal-<?= $booking_id; ?>"
                                    data-modal-toggle="admin-decline-modal-<?= $booking_id; ?>" type="button"
                                    class="inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-times mr-1.5"></i>
                                    Decline
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Admin Decline Modal -->
                        <div id="admin-decline-modal-<?= $booking_id; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-lg max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Decline Booking
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-red-800 hover:bg-red-900"
                                            data-modal-hide="admin-decline-modal-<?= $booking_id; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <form action="inc/inc.booking.php" method="POST" id="adminDeclineForm-<?= $booking_id; ?>">
                                        <div class="p-5 bg-white rounded-lg space-y-4">
                                            <!-- Admin Section -->
                                            <div class="bg-gray-800 text-white p-4 rounded-lg">
                                                <h4 class="text-lg font-semibold mb-2">Admin:</h4>
                                                <span>Must give a reason for declining a booking.</span>

                                                <!-- Choose reason or write your own -->
                                                <div class="flex items-center my-4 mb-2">
                                                    <span class="text-sm">Choose a reason or write your own:</span>
                                                </div>

                                                <!-- Predefined Reasons -->
                                                <div class="space-y-2 mb-4">
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="admin_decline_reason" value="Policy violation"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Policy violation</span>
                                                    </label>

                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="admin_decline_reason" value="Incomplete information"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Incomplete information</span>
                                                    </label>

                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="admin_decline_reason" value="other"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Other: [Text box]</span>
                                                    </label>
                                                </div>

                                                <!-- Custom Reason Text Box -->
                                                <div class="mt-3">
                                                    <textarea name="custom_admin_decline_reason" id="customAdminDeclineReason-<?= $booking_id; ?>"
                                                        placeholder="Please specify your reason..."
                                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-400 focus:border-pink-400 text-gray-900 text-sm"
                                                        rows="3" disabled></textarea>
                                                </div>
                                            </div>

                                            <!-- Booking Reference -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                                <p class="text-sm font-semibold text-gray-800 mt-1">
                                                    <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                                </p>
                                            </div>

                                            <!-- Booking Details -->
                                            <div class="space-y-2">
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">Operator:</span>
                                                    <span
                                                        class="text-sm font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">Destination:</span>
                                                    <span
                                                        class="text-sm font-medium text-gray-900"><?= htmlspecialchars($destination); ?></span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden Form Fields -->
                                        <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                        <input type="hidden" name="booking_id" value="<?= $booking_id; ?>">
                                        <input type="hidden" name="referenceNumber"
                                            value="<?= htmlspecialchars($referenceNumber); ?>">
                                        <input type="hidden" name="mthoResponse" value="Declined">
                                        <input type="hidden" name="ctrlNum" value="0">

                                        <!-- Modal Footer -->
                                        <div
                                            class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                            <button data-modal-hide="admin-decline-modal-<?= $booking_id; ?>" type="button"
                                                class="py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                            <button type="submit" name="mthoResponseBtn" id="submitAdminDeclineBtn-<?= $booking_id; ?>"
                                                class="py-2 px-4 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200 opacity-50 cursor-not-allowed"
                                                disabled>
                                                <i class="fas fa-paper-plane mr-1"></i> Submit Decline
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <?php
                    }
                } else {
                    ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-blue-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-clock text-blue-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Pending Bookings</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">Pending bookings will appear here when they are
                                    awaiting approval.</p>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center border-red-200 bg-red-50">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-red-100 p-4 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-red-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-red-600 text-sm mb-4"><?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="location.reload()" class="action-button btn-view">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <script>
        // Handle admin decline reason selection
        document.addEventListener('change', function(e) {
            if (e.target.name === 'admin_decline_reason') {
                const form = e.target.closest('form');
                const bookingId = form.id.replace('adminDeclineForm-', '');
                const submitBtn = document.getElementById(`submitAdminDeclineBtn-${bookingId}`);
                const customReasonTextarea = document.getElementById(`customAdminDeclineReason-${bookingId}`);

                // Enable submit button when any reason is selected
                submitBtn.disabled = false;
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                // Handle "other" option
                if (e.target.value === 'other') {
                    customReasonTextarea.disabled = false;
                    customReasonTextarea.classList.remove('bg-gray-100', 'text-gray-500');
                    customReasonTextarea.classList.add('bg-white');
                    customReasonTextarea.focus();
                } else {
                    customReasonTextarea.disabled = true;
                    customReasonTextarea.classList.add('bg-gray-100', 'text-gray-500');
                    customReasonTextarea.classList.remove('bg-white');
                    customReasonTextarea.value = '';
                }
            }
        });

        // Handle custom reason textarea input
        document.addEventListener('input', function(e) {
            if (e.target.name === 'custom_admin_decline_reason') {
                const form = e.target.closest('form');
                const bookingId = form.id.replace('adminDeclineForm-', '');
                const submitBtn = document.getElementById(`submitAdminDeclineBtn-${bookingId}`);
                const otherRadio = form.querySelector('input[name="admin_decline_reason"][value="other"]');

                if (otherRadio && otherRadio.checked) {
                    if (e.target.value.trim() === '') {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    } else {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            }
        });

        // Handle form submission validation
        document.addEventListener('submit', function(e) {
            if (e.target.id && e.target.id.startsWith('adminDeclineForm-')) {
                const form = e.target;
                const selectedReason = form.querySelector('input[name="admin_decline_reason"]:checked');

                if (!selectedReason) {
                    e.preventDefault();
                    alert('Please select a reason for declining this booking.');
                    return false;
                }

                if (selectedReason.value === 'other') {
                    const customReason = form.querySelector('textarea[name="custom_admin_decline_reason"]').value.trim();
                    if (customReason === '') {
                        e.preventDefault();
                        alert('Please provide a custom reason for declining this booking.');
                        return false;
                    }
                }

                // Confirmation prompt
                if (!confirm('Are you sure you want to decline this booking?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Reset modal state when closed
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-modal-hide') && e.target.getAttribute('data-modal-hide').includes('admin-decline-modal')) {
                const modalId = e.target.getAttribute('data-modal-hide');
                const bookingId = modalId.replace('admin-decline-modal-', '');

                // Reset form
                const form = document.getElementById(`adminDeclineForm-${bookingId}`);
                if (form) {
                    form.reset();

                    // Reset submit button
                    const submitBtn = document.getElementById(`submitAdminDeclineBtn-${bookingId}`);
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    }

                    // Reset custom reason textarea
                    const customReasonTextarea = document.getElementById(`customAdminDeclineReason-${bookingId}`);
                    if (customReasonTextarea) {
                        customReasonTextarea.disabled = true;
                        customReasonTextarea.classList.add('bg-gray-100', 'text-gray-500');
                        customReasonTextarea.classList.remove('bg-white');
                        customReasonTextarea.value = '';
                    }
                }
            }
        });
    </script>

    <?php
    require '_footer.php';
    ?>